import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../data/datasources/mock_data_source.dart';
import '../../../../domain/entities/match.dart';
import '../../../widgets/match_card.dart';

class MatchesTab extends StatefulWidget {
  const MatchesTab({super.key});

  @override
  State<MatchesTab> createState() => _MatchesTabState();
}

class _MatchesTabState extends State<MatchesTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<Match> _matches = MockDataSource.getMockMatches();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'المباريات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              // TODO: Show date picker
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: Show filter options
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          tabs: const [
            Tab(text: 'اليوم'),
            Tab(text: 'القادمة'),
            Tab(text: 'المنتهية'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodayMatches(),
          _buildUpcomingMatches(),
          _buildFinishedMatches(),
        ],
      ),
    );
  }

  Widget _buildTodayMatches() {
    final todayMatches = _matches.where((match) {
      final today = DateTime.now();
      final matchDate = match.scheduledTime;
      return matchDate.year == today.year &&
          matchDate.month == today.month &&
          matchDate.day == today.day;
    }).toList();

    if (todayMatches.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.sports_soccer_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد مباريات اليوم',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: todayMatches.length,
      itemBuilder: (context, index) {
        return MatchCard(match: todayMatches[index]);
      },
    );
  }

  Widget _buildUpcomingMatches() {
    final upcomingMatches = _matches.where((match) {
      return match.scheduledTime.isAfter(DateTime.now()) &&
          match.status == MatchStatus.scheduled;
    }).toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: upcomingMatches.length,
      itemBuilder: (context, index) {
        return MatchCard(match: upcomingMatches[index]);
      },
    );
  }

  Widget _buildFinishedMatches() {
    final finishedMatches = _matches.where((match) {
      return match.status == MatchStatus.finished;
    }).toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: finishedMatches.length,
      itemBuilder: (context, index) {
        return MatchCard(match: finishedMatches[index]);
      },
    );
  }
}
