class AppConstants {
  // App Info
  static const String appName = 'Football Fan App';
  static const String appVersion = '1.0.0';
  
  // API Constants (for future use)
  static const String baseUrl = 'https://api.footballfanapp.com';
  static const int connectionTimeout = 30000;
  static const int receiveTimeout = 30000;
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String favoriteTeamsKey = 'favorite_teams';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Image Constants
  static const String defaultUserAvatar = 'assets/images/default_avatar.png';
  static const String defaultTeamLogo = 'assets/images/default_team_logo.png';
  
  // Validation
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
  static const int minPasswordLength = 6;
  static const int maxPostContentLength = 5000;
  static const int maxCommentLength = 1000;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  
  // Leagues and Competitions
  static const List<String> europeanLeagues = [
    'Premier League',
    'La Liga',
    'Serie A',
    'Bundesliga',
    'Ligue 1',
    'Champions League',
    'Europa League',
  ];
  
  static const List<String> arabLeagues = [
    'Saudi Pro League',
    'Egyptian Premier League',
    'UAE Pro League',
    'Qatar Stars League',
    'Moroccan Botola',
    'Tunisian Ligue Professionnelle 1',
  ];
}
