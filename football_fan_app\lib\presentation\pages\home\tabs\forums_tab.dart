import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class ForumsTab extends StatefulWidget {
  const ForumsTab({super.key});

  @override
  State<ForumsTab> createState() => _ForumsTabState();
}

class _ForumsTabState extends State<ForumsTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'المنتديات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Navigate to search page
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          tabs: const [
            Tab(text: 'الدوريات الأوروبية'),
            Tab(text: 'الدوريات العربية'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildEuropeanLeagues(),
          _buildArabLeagues(),
        ],
      ),
    );
  }

  Widget _buildEuropeanLeagues() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: AppConstants.europeanLeagues.length,
      itemBuilder: (context, index) {
        final league = AppConstants.europeanLeagues[index];
        return _buildLeagueCard(
          league,
          _getLeagueIcon(league),
          _getLeagueColor(league),
        );
      },
    );
  }

  Widget _buildArabLeagues() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: AppConstants.arabLeagues.length,
      itemBuilder: (context, index) {
        final league = AppConstants.arabLeagues[index];
        return _buildLeagueCard(
          league,
          _getLeagueIcon(league),
          _getLeagueColor(league),
        );
      },
    );
  }

  Widget _buildLeagueCard(String league, IconData icon, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to league forum page
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      league,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'مناقشات وأخبار $league',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${(index + 1) * 1234} عضو',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.message_outlined,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${(index + 1) * 567} مشاركة',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getLeagueIcon(String league) {
    if (league.contains('Premier League')) return Icons.sports_soccer;
    if (league.contains('La Liga')) return Icons.sports_soccer;
    if (league.contains('Serie A')) return Icons.sports_soccer;
    if (league.contains('Bundesliga')) return Icons.sports_soccer;
    if (league.contains('Ligue 1')) return Icons.sports_soccer;
    if (league.contains('Champions League')) return Icons.emoji_events;
    if (league.contains('Europa League')) return Icons.emoji_events;
    return Icons.sports_soccer;
  }

  Color _getLeagueColor(String league) {
    if (league.contains('Premier League')) return const Color(0xFF3D195B);
    if (league.contains('La Liga')) return const Color(0xFFFF6B35);
    if (league.contains('Serie A')) return const Color(0xFF004225);
    if (league.contains('Bundesliga')) return const Color(0xFFD20515);
    if (league.contains('Ligue 1')) return const Color(0xFF1E3A8A);
    if (league.contains('Champions League')) return const Color(0xFF0066CC);
    if (league.contains('Europa League')) return const Color(0xFFFF6600);
    return AppTheme.primaryColor;
  }
}
