import 'package:equatable/equatable.dart';
import 'team.dart';

enum MatchStatus { scheduled, live, finished, postponed, cancelled }

class Match extends Equatable {
  final String id;
  final Team homeTeam;
  final Team awayTeam;
  final DateTime scheduledTime;
  final MatchStatus status;
  final String league;
  final String? round;
  final int? homeScore;
  final int? awayScore;
  final int? minute;
  final String? venue;
  final String? referee;
  final List<MatchEvent> events;
  final bool isLive;

  const Match({
    required this.id,
    required this.homeTeam,
    required this.awayTeam,
    required this.scheduledTime,
    required this.status,
    required this.league,
    this.round,
    this.homeScore,
    this.awayScore,
    this.minute,
    this.venue,
    this.referee,
    required this.events,
    required this.isLive,
  });

  @override
  List<Object?> get props => [
        id,
        homeTeam,
        awayTeam,
        scheduledTime,
        status,
        league,
        round,
        homeScore,
        awayScore,
        minute,
        venue,
        referee,
        events,
        isLive,
      ];

  Match copyWith({
    String? id,
    Team? homeTeam,
    Team? awayTeam,
    DateTime? scheduledTime,
    MatchStatus? status,
    String? league,
    String? round,
    int? homeScore,
    int? awayScore,
    int? minute,
    String? venue,
    String? referee,
    List<MatchEvent>? events,
    bool? isLive,
  }) {
    return Match(
      id: id ?? this.id,
      homeTeam: homeTeam ?? this.homeTeam,
      awayTeam: awayTeam ?? this.awayTeam,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      status: status ?? this.status,
      league: league ?? this.league,
      round: round ?? this.round,
      homeScore: homeScore ?? this.homeScore,
      awayScore: awayScore ?? this.awayScore,
      minute: minute ?? this.minute,
      venue: venue ?? this.venue,
      referee: referee ?? this.referee,
      events: events ?? this.events,
      isLive: isLive ?? this.isLive,
    );
  }
}

enum MatchEventType { goal, yellowCard, redCard, substitution, penalty }

class MatchEvent extends Equatable {
  final String id;
  final MatchEventType type;
  final int minute;
  final String playerName;
  final String teamId;
  final String? description;

  const MatchEvent({
    required this.id,
    required this.type,
    required this.minute,
    required this.playerName,
    required this.teamId,
    this.description,
  });

  @override
  List<Object?> get props => [id, type, minute, playerName, teamId, description];
}
