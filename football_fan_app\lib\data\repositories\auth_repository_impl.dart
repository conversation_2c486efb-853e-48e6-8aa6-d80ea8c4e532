import 'package:dartz/dartz.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../datasources/mock_data_source.dart';

class AuthRepositoryImpl implements AuthRepository {
  User? _currentUser;

  @override
  Future<Either<Failure, User>> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Mock validation
      if (email.isEmpty || password.isEmpty) {
        return const Left(ValidationFailure('البريد الإلكتروني وكلمة المرور مطلوبان'));
      }

      if (!email.contains('@')) {
        return const Left(ValidationFailure('البريد الإلكتروني غير صحيح'));
      }

      if (password.length < 6) {
        return const Left(ValidationFailure('كلمة المرور يجب أن تكون 6 أحرف على الأقل'));
      }

      // Find user in mock data
      final userModel = MockDataSource.mockUsers.firstWhere(
        (user) => user.email == email,
        orElse: () => throw const AuthenticationException('المستخدم غير موجود'),
      );

      _currentUser = userModel.toEntity();
      return Right(_currentUser!);
    } on AuthenticationException catch (e) {
      return Left(AuthenticationFailure(e.message));
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, User>> signUpWithEmail({
    required String email,
    required String password,
    required String username,
  }) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock validation
      if (email.isEmpty || password.isEmpty || username.isEmpty) {
        return const Left(ValidationFailure('جميع الحقول مطلوبة'));
      }

      if (!email.contains('@')) {
        return const Left(ValidationFailure('البريد الإلكتروني غير صحيح'));
      }

      if (password.length < 6) {
        return const Left(ValidationFailure('كلمة المرور يجب أن تكون 6 أحرف على الأقل'));
      }

      if (username.length < 3) {
        return const Left(ValidationFailure('اسم المستخدم يجب أن يكون 3 أحرف على الأقل'));
      }

      // Check if user already exists
      final existingUser = MockDataSource.mockUsers.any(
        (user) => user.email == email || user.username == username,
      );

      if (existingUser) {
        return const Left(ValidationFailure('المستخدم موجود بالفعل'));
      }

      // Create new user
      final newUser = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        username: username,
        email: email,
        favoriteTeams: [],
        createdAt: DateTime.now(),
        postsCount: 0,
        commentsCount: 0,
        followersCount: 0,
        followingCount: 0,
        isVerified: false,
      );

      _currentUser = newUser;
      return Right(newUser);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, User>> signInWithGoogle() async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      _currentUser = MockDataSource.mockUsers.first.toEntity();
      return Right(_currentUser!);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, User>> signInWithFacebook() async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      _currentUser = MockDataSource.mockUsers.first.toEntity();
      return Right(_currentUser!);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, User>> signInWithApple() async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      _currentUser = MockDataSource.mockUsers.first.toEntity();
      return Right(_currentUser!);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _currentUser = null;
      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      if (_currentUser == null) {
        return const Left(AuthenticationFailure('لم يتم تسجيل الدخول'));
      }
      return Right(_currentUser!);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> isSignedIn() async {
    try {
      return Right(_currentUser != null);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword({required String email}) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      
      if (!email.contains('@')) {
        return const Left(ValidationFailure('البريد الإلكتروني غير صحيح'));
      }

      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, User>> updateProfile({
    String? displayName,
    String? avatarUrl,
    List<String>? favoriteTeams,
  }) async {
    try {
      if (_currentUser == null) {
        return const Left(AuthenticationFailure('لم يتم تسجيل الدخول'));
      }

      await Future.delayed(const Duration(seconds: 1));

      _currentUser = _currentUser!.copyWith(
        displayName: displayName,
        avatarUrl: avatarUrl,
        favoriteTeams: favoriteTeams,
      );

      return Right(_currentUser!);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    try {
      await Future.delayed(const Duration(seconds: 2));
      _currentUser = null;
      return const Right(null);
    } catch (e) {
      return const Left(ServerFailure());
    }
  }
}
