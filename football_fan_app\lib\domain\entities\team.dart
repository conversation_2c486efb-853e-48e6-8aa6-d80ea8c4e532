import 'package:equatable/equatable.dart';

class Team extends Equatable {
  final String id;
  final String name;
  final String shortName;
  final String logoUrl;
  final String league;
  final String country;
  final String? description;
  final int foundedYear;
  final String? stadiumName;
  final int followersCount;
  final bool isVerified;

  const Team({
    required this.id,
    required this.name,
    required this.shortName,
    required this.logoUrl,
    required this.league,
    required this.country,
    this.description,
    required this.foundedYear,
    this.stadiumName,
    required this.followersCount,
    required this.isVerified,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        shortName,
        logoUrl,
        league,
        country,
        description,
        foundedYear,
        stadiumName,
        followersCount,
        isVerified,
      ];

  Team copyWith({
    String? id,
    String? name,
    String? shortName,
    String? logoUrl,
    String? league,
    String? country,
    String? description,
    int? foundedYear,
    String? stadiumName,
    int? followersCount,
    bool? isVerified,
  }) {
    return Team(
      id: id ?? this.id,
      name: name ?? this.name,
      shortName: shortName ?? this.shortName,
      logoUrl: logoUrl ?? this.logoUrl,
      league: league ?? this.league,
      country: country ?? this.country,
      description: description ?? this.description,
      foundedYear: foundedYear ?? this.foundedYear,
      stadiumName: stadiumName ?? this.stadiumName,
      followersCount: followersCount ?? this.followersCount,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}
