import 'package:dartz/dartz.dart';
import '../entities/team.dart';
import '../../core/errors/failures.dart';

abstract class TeamsRepository {
  Future<Either<Failure, List<Team>>> getAllTeams();

  Future<Either<Failure, List<Team>>> getTeamsByLeague(String league);

  Future<Either<Failure, Team>> getTeamById(String teamId);

  Future<Either<Failure, List<Team>>> searchTeams(String query);

  Future<Either<Failure, List<Team>>> getPopularTeams({
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Team>>> getFavoriteTeams(String userId);

  Future<Either<Failure, void>> addToFavorites({
    required String userId,
    required String teamId,
  });

  Future<Either<Failure, void>> removeFromFavorites({
    required String userId,
    required String teamId,
  });

  Future<Either<Failure, List<String>>> getAvailableLeagues();
}
