import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/post.dart';
import '../../repositories/posts_repository.dart';
import '../../../core/errors/failures.dart';
import '../usecase.dart';

class GetPosts implements UseCase<List<Post>, GetPostsParams> {
  final PostsRepository repository;

  GetPosts(this.repository);

  @override
  Future<Either<Failure, List<Post>>> call(GetPostsParams params) async {
    return await repository.getPosts(
      page: params.page,
      limit: params.limit,
      teamId: params.teamId,
      leagueId: params.leagueId,
      userId: params.userId,
    );
  }
}

class GetPostsParams extends Equatable {
  final int page;
  final int limit;
  final String? teamId;
  final String? leagueId;
  final String? userId;

  const GetPostsParams({
    this.page = 1,
    this.limit = 20,
    this.teamId,
    this.leagueId,
    this.userId,
  });

  @override
  List<Object?> get props => [page, limit, teamId, leagueId, userId];
}
