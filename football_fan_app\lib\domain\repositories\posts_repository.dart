import 'package:dartz/dartz.dart';
import '../entities/post.dart';
import '../../core/errors/failures.dart';

abstract class PostsRepository {
  Future<Either<Failure, List<Post>>> getPosts({
    int page = 1,
    int limit = 20,
    String? teamId,
    String? leagueId,
    String? userId,
  });

  Future<Either<Failure, Post>> getPostById(String postId);

  Future<Either<Failure, Post>> createPost({
    required String title,
    required String content,
    required PostType type,
    String? teamId,
    String? leagueId,
    List<String>? imageUrls,
    String? videoUrl,
    List<String>? tags,
  });

  Future<Either<Failure, Post>> updatePost({
    required String postId,
    String? title,
    String? content,
    List<String>? tags,
  });

  Future<Either<Failure, void>> deletePost(String postId);

  Future<Either<Failure, Post>> likePost(String postId);

  Future<Either<Failure, Post>> unlikePost(String postId);

  Future<Either<Failure, void>> sharePost(String postId);

  Future<Either<Failure, List<Post>>> getPopularPosts({
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Post>>> getTrendingPosts({
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Post>>> searchPosts({
    required String query,
    int page = 1,
    int limit = 20,
  });
}
