import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String username;
  final String email;
  final String? displayName;
  final String? avatarUrl;
  final List<String> favoriteTeams;
  final DateTime createdAt;
  final DateTime? lastActiveAt;
  final int postsCount;
  final int commentsCount;
  final int followersCount;
  final int followingCount;
  final bool isVerified;

  const User({
    required this.id,
    required this.username,
    required this.email,
    this.displayName,
    this.avatarUrl,
    required this.favoriteTeams,
    required this.createdAt,
    this.lastActiveAt,
    required this.postsCount,
    required this.commentsCount,
    required this.followersCount,
    required this.followingCount,
    required this.isVerified,
  });

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        displayName,
        avatarUrl,
        favoriteTeams,
        createdAt,
        lastActiveAt,
        postsCount,
        commentsCount,
        followersCount,
        followingCount,
        isVerified,
      ];

  User copyWith({
    String? id,
    String? username,
    String? email,
    String? displayName,
    String? avatarUrl,
    List<String>? favoriteTeams,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    int? postsCount,
    int? commentsCount,
    int? followersCount,
    int? followingCount,
    bool? isVerified,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      favoriteTeams: favoriteTeams ?? this.favoriteTeams,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      postsCount: postsCount ?? this.postsCount,
      commentsCount: commentsCount ?? this.commentsCount,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}
