import 'package:equatable/equatable.dart';
import 'user.dart';

enum PostType { text, image, video, poll }

class Post extends Equatable {
  final String id;
  final String title;
  final String content;
  final PostType type;
  final User author;
  final String? teamId;
  final String? leagueId;
  final List<String> imageUrls;
  final String? videoUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final bool isLiked;
  final bool isPinned;
  final bool isLocked;
  final List<String> tags;

  const Post({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.author,
    this.teamId,
    this.leagueId,
    required this.imageUrls,
    this.videoUrl,
    required this.createdAt,
    this.updatedAt,
    required this.likesCount,
    required this.commentsCount,
    required this.sharesCount,
    required this.isLiked,
    required this.isPinned,
    required this.isLocked,
    required this.tags,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        type,
        author,
        teamId,
        leagueId,
        imageUrls,
        videoUrl,
        createdAt,
        updatedAt,
        likesCount,
        commentsCount,
        sharesCount,
        isLiked,
        isPinned,
        isLocked,
        tags,
      ];

  Post copyWith({
    String? id,
    String? title,
    String? content,
    PostType? type,
    User? author,
    String? teamId,
    String? leagueId,
    List<String>? imageUrls,
    String? videoUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
    bool? isLiked,
    bool? isPinned,
    bool? isLocked,
    List<String>? tags,
  }) {
    return Post(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      author: author ?? this.author,
      teamId: teamId ?? this.teamId,
      leagueId: leagueId ?? this.leagueId,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrl: videoUrl ?? this.videoUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      isLiked: isLiked ?? this.isLiked,
      isPinned: isPinned ?? this.isPinned,
      isLocked: isLocked ?? this.isLocked,
      tags: tags ?? this.tags,
    );
  }
}
