import 'package:dartz/dartz.dart';
import '../entities/match.dart';
import '../../core/errors/failures.dart';

abstract class MatchesRepository {
  Future<Either<Failure, List<Match>>> getTodayMatches();

  Future<Either<Failure, List<Match>>> getUpcomingMatches({
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Match>>> getFinishedMatches({
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Match>>> getLiveMatches();

  Future<Either<Failure, Match>> getMatchById(String matchId);

  Future<Either<Failure, List<Match>>> getMatchesByTeam({
    required String teamId,
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Match>>> getMatchesByLeague({
    required String league,
    int page = 1,
    int limit = 20,
  });

  Future<Either<Failure, List<Match>>> getMatchesByDate({
    required DateTime date,
  });

  Future<Either<Failure, List<Match>>> getFavoriteTeamsMatches({
    required List<String> teamIds,
    int page = 1,
    int limit = 20,
  });
}
