import 'package:dartz/dartz.dart';
import '../entities/user.dart';
import '../../core/errors/failures.dart';

abstract class AuthRepository {
  Future<Either<Failure, User>> signInWithEmail({
    required String email,
    required String password,
  });

  Future<Either<Failure, User>> signUpWithEmail({
    required String email,
    required String password,
    required String username,
  });

  Future<Either<Failure, User>> signInWithGoogle();

  Future<Either<Failure, User>> signInWithFacebook();

  Future<Either<Failure, User>> signInWithApple();

  Future<Either<Failure, void>> signOut();

  Future<Either<Failure, User>> getCurrentUser();

  Future<Either<Failure, bool>> isSignedIn();

  Future<Either<Failure, void>> resetPassword({required String email});

  Future<Either<Failure, User>> updateProfile({
    String? displayName,
    String? avatarUrl,
    List<String>? favoriteTeams,
  });

  Future<Either<Failure, void>> deleteAccount();
}
