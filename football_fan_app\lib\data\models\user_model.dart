import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends User {
  const UserModel({
    required super.id,
    required super.username,
    required super.email,
    super.displayName,
    super.avatarUrl,
    required super.favoriteTeams,
    required super.createdAt,
    super.lastActiveAt,
    required super.postsCount,
    required super.commentsCount,
    required super.followersCount,
    required super.followingCount,
    required super.isVerified,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      username: user.username,
      email: user.email,
      displayName: user.displayName,
      avatarUrl: user.avatarUrl,
      favoriteTeams: user.favoriteTeams,
      createdAt: user.createdAt,
      lastActiveAt: user.lastActiveAt,
      postsCount: user.postsCount,
      commentsCount: user.commentsCount,
      followersCount: user.followersCount,
      followingCount: user.followingCount,
      isVerified: user.isVerified,
    );
  }

  User toEntity() {
    return User(
      id: id,
      username: username,
      email: email,
      displayName: displayName,
      avatarUrl: avatarUrl,
      favoriteTeams: favoriteTeams,
      createdAt: createdAt,
      lastActiveAt: lastActiveAt,
      postsCount: postsCount,
      commentsCount: commentsCount,
      followersCount: followersCount,
      followingCount: followingCount,
      isVerified: isVerified,
    );
  }
}
