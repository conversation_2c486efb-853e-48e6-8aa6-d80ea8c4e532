import '../models/user_model.dart';
import '../../domain/entities/team.dart';
import '../../domain/entities/post.dart';
import '../../domain/entities/match.dart';

class MockDataSource {
  // Mock Users
  static final List<UserModel> mockUsers = [
    UserModel(
      id: '1',
      username: 'ahmed_madrid',
      email: '<EMAIL>',
      displayName: 'أحمد محمد',
      avatarUrl: 'https://example.com/avatar1.jpg',
      favoriteTeams: ['real_madrid', 'al_ahly'],
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      lastActiveAt: DateTime.now().subtract(const Duration(hours: 2)),
      postsCount: 45,
      commentsCount: 123,
      followersCount: 234,
      followingCount: 89,
      isVerified: true,
    ),
    UserModel(
      id: '2',
      username: 'sara_barca',
      email: '<EMAIL>',
      displayName: 'سارة أحمد',
      avatarUrl: 'https://example.com/avatar2.jpg',
      favoriteTeams: ['barcelona', 'zamalek'],
      createdAt: DateTime.now().subtract(const Duration(days: 200)),
      lastActiveAt: DateTime.now().subtract(const Duration(minutes: 30)),
      postsCount: 67,
      commentsCount: 189,
      followersCount: 456,
      followingCount: 123,
      isVerified: false,
    ),
  ];

  // Mock Teams
  static final List<Team> mockTeams = [
    const Team(
      id: 'real_madrid',
      name: 'Real Madrid',
      shortName: 'RMA',
      logoUrl: 'https://example.com/real_madrid_logo.png',
      league: 'La Liga',
      country: 'Spain',
      description: 'نادي ريال مدريد الإسباني',
      foundedYear: 1902,
      stadiumName: 'Santiago Bernabéu',
      followersCount: 2500000,
      isVerified: true,
    ),
    const Team(
      id: 'barcelona',
      name: 'FC Barcelona',
      shortName: 'BAR',
      logoUrl: 'https://example.com/barcelona_logo.png',
      league: 'La Liga',
      country: 'Spain',
      description: 'نادي برشلونة الإسباني',
      foundedYear: 1899,
      stadiumName: 'Camp Nou',
      followersCount: 2300000,
      isVerified: true,
    ),
    const Team(
      id: 'al_ahly',
      name: 'Al Ahly',
      shortName: 'AHL',
      logoUrl: 'https://example.com/al_ahly_logo.png',
      league: 'Egyptian Premier League',
      country: 'Egypt',
      description: 'النادي الأهلي المصري',
      foundedYear: 1907,
      stadiumName: 'Al Ahly WE Stadium',
      followersCount: 1800000,
      isVerified: true,
    ),
  ];

  // Mock Posts
  static List<Post> getMockPosts() {
    return [
      Post(
        id: '1',
        title: 'ريال مدريد يفوز بالكلاسيكو!',
        content: 'مباراة رائعة من ريال مدريد أمام برشلونة بنتيجة 3-1. أداء متميز من بنزيما وفينيسيوس.',
        type: PostType.text,
        author: mockUsers[0].toEntity(),
        teamId: 'real_madrid',
        leagueId: 'la_liga',
        imageUrls: ['https://example.com/clasico1.jpg'],
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        likesCount: 245,
        commentsCount: 67,
        sharesCount: 23,
        isLiked: false,
        isPinned: true,
        isLocked: false,
        tags: ['كلاسيكو', 'ريال_مدريد', 'برشلونة'],
      ),
      Post(
        id: '2',
        title: 'تحليل: أداء برشلونة في الموسم الحالي',
        content: 'نظرة على أداء برشلونة هذا الموسم والتحديات التي يواجهها الفريق.',
        type: PostType.text,
        author: mockUsers[1].toEntity(),
        teamId: 'barcelona',
        leagueId: 'la_liga',
        imageUrls: [],
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        likesCount: 123,
        commentsCount: 34,
        sharesCount: 12,
        isLiked: true,
        isPinned: false,
        isLocked: false,
        tags: ['برشلونة', 'تحليل'],
      ),
    ];
  }

  // Mock Matches
  static List<Match> getMockMatches() {
    return [
      Match(
        id: '1',
        homeTeam: mockTeams[0],
        awayTeam: mockTeams[1],
        scheduledTime: DateTime.now().add(const Duration(hours: 3)),
        status: MatchStatus.scheduled,
        league: 'La Liga',
        round: 'الجولة 15',
        venue: 'Santiago Bernabéu',
        referee: 'أنطونيو ماتيو',
        events: [],
        isLive: false,
      ),
      Match(
        id: '2',
        homeTeam: mockTeams[2],
        awayTeam: mockTeams[0],
        scheduledTime: DateTime.now().subtract(const Duration(hours: 2)),
        status: MatchStatus.finished,
        league: 'Friendly',
        homeScore: 1,
        awayScore: 2,
        venue: 'Al Ahly WE Stadium',
        referee: 'محمد الحنفي',
        events: [],
        isLive: false,
      ),
    ];
  }
}
